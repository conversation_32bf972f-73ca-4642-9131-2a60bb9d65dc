/**
 * Three.js Text Geometry Example - Modern ES Modules
 * 复刻 webgl_geometry_text_shapes 示例
 * 显示中文："邱大山，你真棒！"
 */

import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { FontLoader } from 'three/addons/loaders/FontLoader.js';
import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

let camera, scene, renderer, controls;
let group, textMeshes = [];
let font = null;

const text = "邱大山，你真棒！";
const textParams = {
    size: 60,
    height: 15,
    curveSegments: 4,
    bevelEnabled: true,
    bevelThickness: 1,
    bevelSize: 0.5
};

let targetRotation = 0;
let mouseX = 0, mouseY = 0;
let windowHalfX = window.innerWidth / 2;
let windowHalfY = window.innerHeight / 2;

init();
animate();

function init() {
    // 场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x000000);
    scene.fog = new THREE.Fog(0x000000, 250, 1400);

    // 相机
    camera = new THREE.PerspectiveCamera(30, window.innerWidth / window.innerHeight, 1, 1500);
    camera.position.set(0, 400, 700);

    // 渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    document.getElementById('container').appendChild(renderer.domElement);

    // 控制器
    controls = new OrbitControls(camera, renderer.domElement);
    controls.target.set(0, 0, 0);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;

    // 光照
    setupLights();

    // 创建文字组
    group = new THREE.Group();
    scene.add(group);

    // 加载字体并创建文字
    loadFontAndCreateText();

    // 事件监听
    setupEventListeners();

    // 隐藏加载界面
    setTimeout(() => {
        const loading = document.getElementById('loading');
        if (loading) loading.style.display = 'none';
    }, 2000);
}

function setupLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1).normalize();
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 点光源
    const pointLight = new THREE.PointLight(0xffffff, 1, 1000);
    pointLight.position.set(0, 100, 90);
    scene.add(pointLight);
}

function loadFontAndCreateText() {
    const loader = new FontLoader();
    
    // 尝试加载字体
    loader.load(
        'https://threejs.org/examples/fonts/helvetiker_regular.typeface.json',
        function(loadedFont) {
            font = loadedFont;
            createTextGeometry();
        },
        undefined,
        function(error) {
            console.log('Font loading failed, using fallback method');
            createChineseTextMeshes();
        }
    );
}

function createTextGeometry() {
    // 尝试创建英文文字作为示例
    try {
        const geometry = new TextGeometry('QIU DA SHAN\nNI ZHEN BANG!', {
            font: font,
            size: textParams.size,
            height: textParams.height,
            curveSegments: textParams.curveSegments,
            bevelEnabled: textParams.bevelEnabled,
            bevelThickness: textParams.bevelThickness,
            bevelSize: textParams.bevelSize
        });

        geometry.computeBoundingBox();
        const centerOffsetX = -0.5 * (geometry.boundingBox.max.x - geometry.boundingBox.min.x);
        const centerOffsetY = -0.5 * (geometry.boundingBox.max.y - geometry.boundingBox.min.y);

        const materials = [
            new THREE.MeshPhongMaterial({ color: 0xffffff, flatShading: true }), // front
            new THREE.MeshPhongMaterial({ color: 0x666666 }) // side
        ];

        const textMesh = new THREE.Mesh(geometry, materials);
        textMesh.position.x = centerOffsetX;
        textMesh.position.y = centerOffsetY;
        textMesh.position.z = 0;
        textMesh.castShadow = true;
        textMesh.receiveShadow = true;

        group.add(textMesh);
        textMeshes.push(textMesh);

        // 添加镜像
        const mirrorMesh = textMesh.clone();
        mirrorMesh.position.y = -centerOffsetY - 100;
        mirrorMesh.position.z = textParams.height;
        mirrorMesh.rotation.x = Math.PI;
        group.add(mirrorMesh);
        textMeshes.push(mirrorMesh);

    } catch (error) {
        console.log('TextGeometry creation failed, using fallback');
        createChineseTextMeshes();
    }
}

function createChineseTextMeshes() {
    const characters = ['邱', '大', '山', '你', '真', '棒', '！'];
    const spacing = 120;
    const startX = -(characters.length - 1) * spacing / 2;

    characters.forEach((char, index) => {
        const mesh = createChineseCharacterMesh(char);
        mesh.position.x = startX + index * spacing;
        mesh.position.y = 0;
        mesh.position.z = 0;
        
        group.add(mesh);
        textMeshes.push(mesh);
    });
}

function createChineseCharacterMesh(character) {
    // 创建基础几何体
    const geometry = new THREE.ExtrudeGeometry(
        createCharacterShape(),
        {
            depth: textParams.height,
            bevelEnabled: textParams.bevelEnabled,
            bevelThickness: textParams.bevelThickness,
            bevelSize: textParams.bevelSize,
            bevelSegments: 3,
            curveSegments: textParams.curveSegments
        }
    );

    // 创建文字纹理
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 256;

    // 绘制文字
    context.fillStyle = '#ffffff';
    context.font = 'bold 180px "Microsoft YaHei", "SimHei", "黑体", Arial, sans-serif';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(character, canvas.width/2, canvas.height/2);

    // 添加发光效果
    context.shadowColor = '#00aaff';
    context.shadowBlur = 20;
    context.fillText(character, canvas.width/2, canvas.height/2);

    const texture = new THREE.CanvasTexture(canvas);

    // 创建材质
    const materials = [
        new THREE.MeshPhongMaterial({ map: texture, color: 0xffffff }), // 正面
        new THREE.MeshPhongMaterial({ color: 0x666666 }) // 侧面
    ];

    const mesh = new THREE.Mesh(geometry, materials);
    mesh.castShadow = true;
    mesh.receiveShadow = true;

    return mesh;
}

function createCharacterShape() {
    const shape = new THREE.Shape();
    const size = textParams.size;
    
    // 创建圆角矩形形状
    const x = -size/2, y = -size/2;
    const width = size, height = size;
    const radius = size * 0.1;

    shape.moveTo(x, y + radius);
    shape.lineTo(x, y + height - radius);
    shape.quadraticCurveTo(x, y + height, x + radius, y + height);
    shape.lineTo(x + width - radius, y + height);
    shape.quadraticCurveTo(x + width, y + height, x + width, y + height - radius);
    shape.lineTo(x + width, y + radius);
    shape.quadraticCurveTo(x + width, y, x + width - radius, y);
    shape.lineTo(x + radius, y);
    shape.quadraticCurveTo(x, y, x, y + radius);

    return shape;
}

function setupEventListeners() {
    // 鼠标事件
    document.addEventListener('mousemove', onDocumentMouseMove, false);
    document.addEventListener('mousedown', onDocumentMouseDown, false);
    
    // 键盘事件
    document.addEventListener('keydown', onDocumentKeyDown, false);
    
    // 窗口大小调整
    window.addEventListener('resize', onWindowResize, false);
}

function onDocumentMouseMove(event) {
    mouseX = (event.clientX - windowHalfX) * 0.001;
    mouseY = (event.clientY - windowHalfY) * 0.001;
}

function onDocumentMouseDown(event) {
    targetRotation += Math.PI * 0.1;
}

function onDocumentKeyDown(event) {
    switch(event.code) {
        case 'Space':
            event.preventDefault();
            targetRotation += Math.PI * 2;
            break;
        case 'KeyR':
            camera.position.set(0, 400, 700);
            targetRotation = 0;
            group.rotation.y = 0;
            break;
        case 'KeyC':
            // 切换到中文显示
            while(group.children.length > 0) {
                group.remove(group.children[0]);
            }
            textMeshes = [];
            createChineseTextMeshes();
            break;
        case 'KeyE':
            // 切换到英文显示
            if (font) {
                while(group.children.length > 0) {
                    group.remove(group.children[0]);
                }
                textMeshes = [];
                createTextGeometry();
            }
            break;
    }
}

function onWindowResize() {
    windowHalfX = window.innerWidth / 2;
    windowHalfY = window.innerHeight / 2;

    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();

    renderer.setSize(window.innerWidth, window.innerHeight);
}

function animate() {
    requestAnimationFrame(animate);

    const time = Date.now() * 0.001;

    // 平滑旋转
    group.rotation.y += (targetRotation - group.rotation.y) * 0.05;

    // 文字动画
    textMeshes.forEach((mesh, index) => {
        const offset = index * 0.3;
        
        // 浮动效果
        mesh.position.y += Math.sin(time * 2 + offset) * 0.5;
        
        // 独立旋转
        mesh.rotation.z = Math.sin(time * 1.5 + offset) * 0.1;
        
        // 颜色变化
        if (mesh.material && mesh.material[0]) {
            const hue = (time * 0.2 + index * 0.1) % 1;
            mesh.material[0].color.setHSL(hue, 0.8, 0.7);
        } else if (mesh.material && mesh.material.color) {
            const hue = (time * 0.2 + index * 0.1) % 1;
            mesh.material.color.setHSL(hue, 0.8, 0.7);
        }
    });

    // 相机跟随鼠标
    camera.position.x += (mouseX * 200 - camera.position.x) * 0.05;
    camera.position.y += (-mouseY * 200 - camera.position.y) * 0.05;

    // 更新控制器
    controls.update();

    // 渲染
    renderer.render(scene, camera);
}
