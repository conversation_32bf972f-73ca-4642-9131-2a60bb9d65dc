// 全局变量
let scene, camera, renderer, controls;
let textMesh, particles, particleSystem;
let mouse = new THREE.Vector2();
let time = 0;

// 初始化场景
function init() {
    // 创建场景
    scene = new THREE.Scene();
    scene.fog = new THREE.Fog(0x000000, 50, 200);

    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 0, 50);

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    
    document.getElementById('container').appendChild(renderer.domElement);

    // 创建控制器
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.autoRotate = true;
    controls.autoRotateSpeed = 1;

    // 创建光照
    setupLights();
    
    // 创建粒子系统
    createParticleSystem();
    
    // 创建3D文字
    create3DText();
    
    // 添加事件监听
    setupEventListeners();
    
    // 开始渲染循环
    animate();
    
    // 隐藏加载界面
    setTimeout(() => {
        document.getElementById('loading').style.display = 'none';
    }, 2000);
}

// 设置光照
function setupLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    // 主光源
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(50, 50, 50);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 点光源1 - 红色
    const pointLight1 = new THREE.PointLight(0xff0040, 2, 100);
    pointLight1.position.set(-30, 20, 20);
    scene.add(pointLight1);

    // 点光源2 - 蓝色
    const pointLight2 = new THREE.PointLight(0x0040ff, 2, 100);
    pointLight2.position.set(30, -20, 20);
    scene.add(pointLight2);

    // 点光源3 - 绿色
    const pointLight3 = new THREE.PointLight(0x40ff00, 2, 100);
    pointLight3.position.set(0, 30, -20);
    scene.add(pointLight3);
}

// 创建粒子系统
function createParticleSystem() {
    const particleCount = 1000;
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;
        
        // 随机位置
        positions[i3] = (Math.random() - 0.5) * 200;
        positions[i3 + 1] = (Math.random() - 0.5) * 200;
        positions[i3 + 2] = (Math.random() - 0.5) * 200;
        
        // 随机颜色
        const color = new THREE.Color();
        color.setHSL(Math.random(), 1.0, 0.5);
        colors[i3] = color.r;
        colors[i3 + 1] = color.g;
        colors[i3 + 2] = color.b;
        
        // 随机大小
        sizes[i] = Math.random() * 3 + 1;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    const material = new THREE.PointsMaterial({
        size: 2,
        vertexColors: true,
        transparent: true,
        opacity: 0.8,
        sizeAttenuation: true
    });

    particleSystem = new THREE.Points(geometry, material);
    scene.add(particleSystem);
}

// 创建3D文字
function create3DText() {
    const loader = new THREE.FontLoader();
    
    // 使用内置字体或者加载中文字体
    loader.load('https://threejs.org/examples/fonts/helvetiker_regular.typeface.json', function(font) {
        createTextMesh(font);
    }, undefined, function(error) {
        console.log('字体加载失败，使用备用方案');
        createFallbackText();
    });
}

// 创建文字网格
function createTextMesh(font) {
    // 由于Three.js的TextGeometry对中文支持有限，我们直接使用备用方案
    createFallbackText();
}

// 备用文字创建方案 - 创建中文3D文字效果
function createFallbackText() {
    // 创建文字组
    textMesh = new THREE.Group();

    // 创建多个3D立方体来组成文字效果
    const textBlocks = [];

    // 第一行：邱大山
    const line1Texts = ['邱', '大', '山'];
    for (let i = 0; i < line1Texts.length; i++) {
        const block = createTextBlock(line1Texts[i], i * 18 - 18, 10);
        textBlocks.push(block);
        textMesh.add(block);
    }

    // 第二行：你真棒！
    const line2Texts = ['你', '真', '棒', '！'];
    for (let i = 0; i < line2Texts.length; i++) {
        const block = createTextBlock(line2Texts[i], i * 15 - 22.5, -10);
        textBlocks.push(block);
        textMesh.add(block);
    }

    // 将文字组添加到场景
    scene.add(textMesh);

    // 存储文字块引用以便动画使用
    textMesh.textBlocks = textBlocks;
}

// 创建单个文字块
function createTextBlock(text, x, y) {
    // 创建立方体几何体 - 调整尺寸以更好显示中文
    const geometry = new THREE.BoxGeometry(12, 12, 6);

    // 创建材质
    const material = new THREE.MeshPhongMaterial({
        color: 0xffffff,
        shininess: 100,
        transparent: true,
        opacity: 0.9
    });

    const cube = new THREE.Mesh(geometry, material);
    cube.position.set(x, y, 0);
    cube.castShadow = true;
    cube.receiveShadow = true;

    // 创建文字纹理
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 256;

    // 设置背景为透明
    context.clearRect(0, 0, canvas.width, canvas.height);

    // 设置字体和样式
    context.font = 'bold 140px "Microsoft YaHei", "SimHei", "黑体", Arial, sans-serif';
    context.textAlign = 'center';
    context.textBaseline = 'middle';

    // 添加发光效果背景
    context.shadowColor = '#00ffff';
    context.shadowBlur = 30;
    context.fillStyle = '#ffffff';
    context.fillText(text, canvas.width/2, canvas.height/2);

    // 清除阴影，绘制主文字
    context.shadowBlur = 0;
    context.fillStyle = '#ffffff';
    context.fillText(text, canvas.width/2, canvas.height/2);

    // 添加描边效果
    context.strokeStyle = '#000000';
    context.lineWidth = 3;
    context.strokeText(text, canvas.width/2, canvas.height/2);

    const texture = new THREE.CanvasTexture(canvas);
    cube.material.map = texture;
    cube.material.needsUpdate = true;

    return cube;
}

// 设置事件监听器
function setupEventListeners() {
    // 鼠标移动事件
    window.addEventListener('mousemove', onMouseMove, false);

    // 窗口大小调整事件
    window.addEventListener('resize', onWindowResize, false);

    // 键盘事件
    window.addEventListener('keydown', onKeyDown, false);
}

// 鼠标移动处理
function onMouseMove(event) {
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
}

// 窗口大小调整处理
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// 键盘事件处理
function onKeyDown(event) {
    switch(event.code) {
        case 'Space':
            controls.autoRotate = !controls.autoRotate;
            break;
        case 'KeyR':
            controls.reset();
            break;
    }
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);

    time += 0.01;

    // 更新控制器
    controls.update();

    // 文字动画
    if (textMesh) {
        // 整体浮动效果
        textMesh.position.y = Math.sin(time * 2) * 2;

        // 整体轻微旋转
        textMesh.rotation.y = Math.sin(time * 0.5) * 0.1;
        textMesh.rotation.x = Math.cos(time * 0.3) * 0.05;

        // 鼠标交互
        const targetRotationY = mouse.x * 0.3;
        const targetRotationX = mouse.y * 0.3;
        textMesh.rotation.y += (targetRotationY - textMesh.rotation.y) * 0.05;
        textMesh.rotation.x += (targetRotationX - textMesh.rotation.x) * 0.05;

        // 单个文字块动画
        if (textMesh.textBlocks) {
            textMesh.textBlocks.forEach((block, index) => {
                // 每个文字块的独立动画
                const offset = index * 0.5;

                // 独立浮动
                block.position.z = Math.sin(time * 3 + offset) * 2;

                // 独立旋转
                block.rotation.z = Math.sin(time * 2 + offset) * 0.2;

                // 彩虹色彩变化
                const hue = (time * 0.3 + index * 0.15) % 1;
                block.material.color.setHSL(hue, 1, 0.7);

                // 缩放效果
                const scale = 1 + Math.sin(time * 4 + offset) * 0.1;
                block.scale.set(scale, scale, scale);
            });
        }
    }

    // 粒子动画
    if (particleSystem) {
        particleSystem.rotation.y += 0.002;
        particleSystem.rotation.x += 0.001;

        const positions = particleSystem.geometry.attributes.position.array;
        const colors = particleSystem.geometry.attributes.color.array;

        for (let i = 0; i < positions.length; i += 3) {
            // 粒子运动
            positions[i + 1] += Math.sin(time + i) * 0.1;

            // 颜色变化
            const colorIndex = i;
            const hue = (time * 0.5 + i * 0.01) % 1;
            const color = new THREE.Color();
            color.setHSL(hue, 1.0, 0.5);
            colors[colorIndex] = color.r;
            colors[colorIndex + 1] = color.g;
            colors[colorIndex + 2] = color.b;
        }

        particleSystem.geometry.attributes.position.needsUpdate = true;
        particleSystem.geometry.attributes.color.needsUpdate = true;
    }

    // 渲染场景
    renderer.render(scene, camera);
}

// 启动应用
window.addEventListener('load', init);
