#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔍 腾讯云开发环境检查工具');
console.log('================================');

// 检查配置文件
function checkConfigFile() {
  console.log('\n📄 检查配置文件...');
  
  const configFiles = ['cloudbaserc.json', '.cloudbaserc.json', 'cloudbase.json'];
  let configFound = false;
  
  for (const file of configFiles) {
    if (fs.existsSync(file)) {
      console.log(`✅ 找到配置文件: ${file}`);
      try {
        const config = JSON.parse(fs.readFileSync(file, 'utf8'));
        console.log(`   环境ID: ${config.envId || '未设置'}`);
        console.log(`   版本: ${config.version || '未设置'}`);
        configFound = true;
      } catch (error) {
        console.log(`❌ 配置文件格式错误: ${error.message}`);
      }
      break;
    }
  }
  
  if (!configFound) {
    console.log('⚠️  未找到配置文件');
  }
}

// 检查登录状态
function checkLoginStatus() {
  console.log('\n👤 检查登录状态...');
  try {
    execSync('tcb login', { stdio: 'pipe' });
    console.log('✅ 已登录');
  } catch (error) {
    console.log('❌ 未登录或登录已过期');
    return false;
  }
  return true;
}

// 尝试获取环境列表
function getEnvironmentList() {
  console.log('\n🌍 获取环境列表...');
  
  try {
    // 设置较短的超时时间
    const result = execSync('tcb env list', { 
      stdio: 'pipe', 
      encoding: 'utf8',
      timeout: 30000 // 30秒超时
    });
    
    console.log('✅ 环境列表获取成功:');
    console.log(result);
    return true;
  } catch (error) {
    console.log('❌ 获取环境列表失败:');
    if (error.message.includes('timeout') || error.message.includes('超时')) {
      console.log('   原因: 网络超时');
      console.log('   建议: 检查网络连接或设置代理');
    } else {
      console.log(`   错误: ${error.message}`);
    }
    return false;
  }
}

// 检查网络连接
function checkNetworkConnection() {
  console.log('\n🌐 检查网络连接...');
  
  try {
    // 尝试ping腾讯云域名
    execSync('ping -n 1 cloud.tencent.com', { stdio: 'pipe', timeout: 10000 });
    console.log('✅ 网络连接正常');
    return true;
  } catch (error) {
    console.log('❌ 网络连接异常');
    console.log('   建议: 检查网络设置或防火墙配置');
    return false;
  }
}

// 提供解决方案
function provideSolutions() {
  console.log('\n💡 解决方案建议:');
  console.log('1. 网络问题:');
  console.log('   - 检查网络连接是否正常');
  console.log('   - 如果使用代理，请设置环境变量:');
  console.log('     set HTTP_PROXY=http://proxy-server:port');
  console.log('     set HTTPS_PROXY=http://proxy-server:port');
  
  console.log('\n2. 重新登录:');
  console.log('   tcb logout');
  console.log('   tcb login');
  
  console.log('\n3. 使用腾讯云控制台:');
  console.log('   https://console.cloud.tencent.com/tcb');
  
  console.log('\n4. 检查CLI版本:');
  console.log('   npm update -g @cloudbase/cli');
}

// 主函数
async function main() {
  try {
    // 检查配置文件
    checkConfigFile();
    
    // 检查登录状态
    const isLoggedIn = checkLoginStatus();
    
    if (!isLoggedIn) {
      console.log('\n❌ 请先登录: tcb login');
      return;
    }
    
    // 检查网络连接
    const networkOk = checkNetworkConnection();
    
    // 尝试获取环境列表
    const envListOk = getEnvironmentList();
    
    if (!envListOk) {
      provideSolutions();
    }
    
    console.log('\n📊 检查完成!');
    
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error.message);
  }
}

// 运行检查
main();
