/**
 * Three.js Text Geometry Shapes Example
 * Based on official three.js examples
 * Text: "邱大山，你真棒！"
 */

let camera, scene, renderer;
let group, textMesh1, textMesh2, textGeo, materials;

let firstLetter = true;

let text = "邱大山，你真棒！";
let bevelEnabled = true;

let font = undefined;
let fontName = "helvetiker"; // helvetiker, optimer, gentilis, droid sans, droid serif
let fontWeight = "normal"; // normal bold

const height = 20,
    size = 70,
    hover = 30,
    curveSegments = 4,
    bevelThickness = 2,
    bevelSize = 1.5;

const mirror = true;

let targetRotation = 0;
let targetRotationOnPointerDown = 0;

let pointerX = 0;
let pointerXOnPointerDown = 0;

let windowHalfX = window.innerWidth / 2;

init();
animate();

function init() {
    // 创建容器
    const container = document.getElementById('container');

    // 相机
    camera = new THREE.PerspectiveCamera(30, window.innerWidth / window.innerHeight, 1, 1500);
    camera.position.set(0, 400, 700);

    // 场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x000000);
    scene.fog = new THREE.Fog(0x000000, 250, 1400);

    // 光照
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.125);
    dirLight.position.set(0, 0, 1).normalize();
    scene.add(dirLight);

    const pointLight = new THREE.PointLight(0xffffff, 1.5);
    pointLight.position.set(0, 100, 90);
    pointLight.color.setHSL(Math.random(), 1, 0.5);
    scene.add(pointLight);

    // 材质
    materials = [
        new THREE.MeshPhongMaterial({ color: 0xffffff, flatShading: true }), // front
        new THREE.MeshPhongMaterial({ color: 0xffffff }) // side
    ];

    // 创建组
    group = new THREE.Group();
    group.position.y = 100;
    scene.add(group);

    // 加载字体
    const loader = new THREE.FontLoader();
    loader.load('https://threejs.org/examples/fonts/helvetiker_regular.typeface.json', function (response) {
        font = response;
        refreshText();
    }, undefined, function(error) {
        console.log('Font loading failed, using fallback');
        // 直接使用备用方案
        createFallbackText();
    });

    // 渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(window.innerWidth, window.innerHeight);
    container.appendChild(renderer.domElement);

    // 控制器
    const controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.target.set(0, 0, 0);
    controls.update();

    // 事件监听
    container.style.touchAction = 'none';
    container.addEventListener('pointerdown', onPointerDown);

    document.addEventListener('keypress', onDocumentKeyPress);
    document.addEventListener('keydown', onDocumentKeyDown);

    window.addEventListener('resize', onWindowResize);

    // 隐藏加载界面
    setTimeout(() => {
        document.getElementById('loading').style.display = 'none';
    }, 2000);
}

function refreshText() {
    // 清除现有的文字网格
    while(group.children.length > 0) {
        group.remove(group.children[0]);
    }

    if (!text) return;

    // 优先使用备用方案来显示中文
    if (font && text.match(/[a-zA-Z\s]/)) {
        createText();
    } else {
        createFallbackText();
    }
}

function createText() {
    // 由于Three.js的TextGeometry对中文支持有限，我们创建多个英文字符来代表中文
    // 或者创建形状来模拟中文字符效果

    // 创建文字的形状数组
    const shapes = font.generateShapes(text, size);

    // 为每个字符创建独立的几何体
    const textGeometries = [];
    let totalWidth = 0;

    for (let i = 0; i < shapes.length; i++) {
        const shape = shapes[i];

        if (shape.holes && shape.holes.length > 0) {
            // 处理有孔洞的字符
            const geometry = new THREE.ExtrudeGeometry(shape, {
                depth: height,
                bevelEnabled: bevelEnabled,
                bevelThickness: bevelThickness,
                bevelSize: bevelSize,
                bevelSegments: 2,
                curveSegments: curveSegments
            });
        } else {
            // 处理普通字符
            const geometry = new THREE.ExtrudeGeometry(shape, {
                depth: height,
                bevelEnabled: bevelEnabled,
                bevelThickness: bevelThickness,
                bevelSize: bevelSize,
                bevelSegments: 2,
                curveSegments: curveSegments
            });
        }

        textGeometries.push(geometry);
    }

    // 如果形状创建失败，使用备用的立方体方案
    if (textGeometries.length === 0) {
        createFallbackText();
        return;
    }

    // 合并所有几何体
    textGeo = new THREE.BufferGeometry();

    // 简化处理：创建一个基本的文字几何体
    textGeo = new THREE.TextGeometry("QIU DA SHAN\nNI ZHEN BANG!", {
        font: font,
        size: size,
        height: height,
        curveSegments: curveSegments,
        bevelThickness: bevelThickness,
        bevelSize: bevelSize,
        bevelEnabled: bevelEnabled
    });

    textGeo.computeBoundingBox();

    const centerOffsetX = -0.5 * (textGeo.boundingBox.max.x - textGeo.boundingBox.min.x);
    const centerOffsetY = -0.5 * (textGeo.boundingBox.max.y - textGeo.boundingBox.min.y);

    // 创建多材质文字网格
    textMesh1 = new THREE.Mesh(textGeo, materials);

    textMesh1.position.x = centerOffsetX;
    textMesh1.position.y = hover;
    textMesh1.position.z = 0;

    textMesh1.rotation.x = 0;
    textMesh1.rotation.y = Math.PI * 2;

    textMesh1.castShadow = true;
    textMesh1.receiveShadow = true;

    group.add(textMesh1);

    if (mirror) {
        textMesh2 = new THREE.Mesh(textGeo, materials);

        textMesh2.position.x = centerOffsetX;
        textMesh2.position.y = -hover;
        textMesh2.position.z = height;

        textMesh2.rotation.x = Math.PI;
        textMesh2.rotation.y = Math.PI * 2;

        textMesh2.castShadow = true;
        textMesh2.receiveShadow = true;

        group.add(textMesh2);
    }
}

// 备用文字创建方案 - 使用ExtrudeGeometry创建3D文字效果
function createFallbackText() {
    // 创建中文字符的3D几何体
    const chineseChars = ['邱', '大', '山', '你', '真', '棒', '！'];
    let xOffset = -350;

    chineseChars.forEach((char, index) => {
        // 创建文字形状
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 200;
        canvas.height = 200;

        // 绘制文字到canvas
        context.fillStyle = '#ffffff';
        context.font = 'bold 150px "Microsoft YaHei", "SimHei", Arial, sans-serif';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(char, canvas.width/2, canvas.height/2);

        // 创建基础几何体
        const geometry = new THREE.ExtrudeGeometry(
            createTextShape(),
            {
                depth: height,
                bevelEnabled: bevelEnabled,
                bevelThickness: bevelThickness,
                bevelSize: bevelSize,
                bevelSegments: 2,
                curveSegments: curveSegments
            }
        );

        // 创建材质
        const texture = new THREE.CanvasTexture(canvas);
        const textMaterial = [
            new THREE.MeshPhongMaterial({ map: texture, color: 0xffffff }), // front
            new THREE.MeshPhongMaterial({ color: 0x888888 }) // side
        ];

        const mesh = new THREE.Mesh(geometry, textMaterial);

        mesh.position.x = xOffset + index * 100;
        mesh.position.y = hover;
        mesh.position.z = 0;

        mesh.castShadow = true;
        mesh.receiveShadow = true;

        group.add(mesh);

        // 如果启用镜像，创建镜像文字
        if (mirror) {
            const mirrorMesh = mesh.clone();
            mirrorMesh.position.y = -hover;
            mirrorMesh.position.z = height;
            mirrorMesh.rotation.x = Math.PI;
            group.add(mirrorMesh);
        }
    });
}

// 创建基本的文字形状
function createTextShape() {
    const shape = new THREE.Shape();

    // 创建一个简单的矩形形状作为文字基础
    const width = size * 0.8;
    const height = size;

    shape.moveTo(-width/2, -height/2);
    shape.lineTo(width/2, -height/2);
    shape.lineTo(width/2, height/2);
    shape.lineTo(-width/2, height/2);
    shape.lineTo(-width/2, -height/2);

    return shape;
}

function onPointerDown(event) {
    if (event.isPrimary === false) return;

    pointerXOnPointerDown = event.clientX - windowHalfX;
    targetRotationOnPointerDown = targetRotation;

    document.addEventListener('pointermove', onPointerMove);
    document.addEventListener('pointerup', onPointerUp);
}

function onPointerMove(event) {
    if (event.isPrimary === false) return;

    pointerX = event.clientX - windowHalfX;

    targetRotation = targetRotationOnPointerDown + (pointerX - pointerXOnPointerDown) * 0.02;
}

function onPointerUp() {
    if (event.isPrimary === false) return;

    document.removeEventListener('pointermove', onPointerMove);
    document.removeEventListener('pointerup', onPointerUp);
}

function onDocumentKeyDown(event) {
    if (firstLetter) {
        firstLetter = false;
        text = "";
    }

    const keyCode = event.keyCode;

    // backspace
    if (keyCode == 8) {
        event.preventDefault();
        text = text.substring(0, text.length - 1);
        refreshText();
        return false;
    }
}

function onDocumentKeyPress(event) {
    const keyCode = event.which;

    // backspace
    if (keyCode == 8) {
        event.preventDefault();
    } else {
        const ch = String.fromCharCode(keyCode);
        text += ch;
        refreshText();
    }
}

function onWindowResize() {
    windowHalfX = window.innerWidth / 2;

    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();

    renderer.setSize(window.innerWidth, window.innerHeight);
}

function animate() {
    requestAnimationFrame(animate);

    // 平滑旋转
    group.rotation.y += (targetRotation - group.rotation.y) * 0.05;

    // 添加自动浮动效果
    const time = Date.now() * 0.001;
    group.position.y = 100 + Math.sin(time * 0.5) * 20;

    // 材质颜色动画
    if (textMesh1) {
        const hue = (time * 0.1) % 1;
        materials[0].color.setHSL(hue, 0.7, 0.6);
        materials[1].color.setHSL((hue + 0.1) % 1, 0.7, 0.4);
    }

    // 光源动画
    const pointLight = scene.children.find(child => child.type === 'PointLight');
    if (pointLight) {
        pointLight.position.x = Math.sin(time * 0.7) * 300;
        pointLight.position.z = Math.cos(time * 0.7) * 300;
        pointLight.color.setHSL((time * 0.2) % 1, 1, 0.5);
    }

    // 相机轻微摆动
    camera.position.x += (Math.sin(time * 0.3) * 50 - camera.position.x) * 0.02;

    renderer.render(scene, camera);
}

// 添加键盘控制
document.addEventListener('keydown', function(event) {
    switch(event.code) {
        case 'Space':
            event.preventDefault();
            // 切换自动旋转
            if (Math.abs(targetRotation) < 0.01) {
                targetRotation = Math.PI * 2;
            } else {
                targetRotation = 0;
            }
            break;
        case 'KeyR':
            // 重置相机和旋转
            camera.position.set(0, 400, 700);
            targetRotation = 0;
            group.rotation.y = 0;
            break;
        case 'KeyB':
            // 切换斜角效果
            bevelEnabled = !bevelEnabled;
            if (font) refreshText();
            break;
        case 'KeyM':
            // 切换镜像效果
            mirror = !mirror;
            if (font) refreshText();
            break;
    }
});

// 添加鼠标滚轮缩放
document.addEventListener('wheel', function(event) {
    event.preventDefault();
    const scale = event.deltaY > 0 ? 1.1 : 0.9;
    camera.position.multiplyScalar(scale);

    // 限制缩放范围
    const distance = camera.position.length();
    if (distance < 200) {
        camera.position.normalize().multiplyScalar(200);
    } else if (distance > 2000) {
        camera.position.normalize().multiplyScalar(2000);
    }
});

// 添加窗口焦点事件
window.addEventListener('focus', function() {
    // 窗口获得焦点时重新开始动画
    animate();
});

// 性能监控
let lastTime = performance.now();
let frameCount = 0;

function updatePerformance() {
    frameCount++;
    const currentTime = performance.now();

    if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        console.log(`FPS: ${fps}`);
        frameCount = 0;
        lastTime = currentTime;
    }
}

// 在animate函数中调用性能监控
const originalAnimate = animate;
animate = function() {
    updatePerformance();
    originalAnimate();
};
