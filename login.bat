@echo off
chcp 65001 >nul
echo.
echo 🚀 腾讯云开发环境登录助手
echo ================================
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Node.js，请先安装Node.js
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js已安装

REM 检查npm是否可用
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm不可用
    pause
    exit /b 1
)

echo ✅ npm可用

REM 检查cloudbase CLI是否已安装
cloudbase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo 📦 未检测到腾讯云开发CLI，正在安装...
    npm install -g @cloudbase/cli
    if %errorlevel% neq 0 (
        echo ❌ CLI安装失败
        pause
        exit /b 1
    )
    echo ✅ CLI安装成功
) else (
    echo ✅ 腾讯云开发CLI已安装
)

echo.
echo 🔐 开始登录腾讯云开发...
echo.
echo 请选择登录方式：
echo 1. 微信扫码登录（推荐）
echo 2. API密钥登录
echo.
set /p choice=请输入选择 (1/2): 

if "%choice%"=="1" (
    echo.
    echo 请使用微信扫描二维码登录...
    cloudbase login
) else if "%choice%"=="2" (
    echo.
    echo 使用API密钥登录...
    cloudbase login --key
) else (
    echo.
    echo 无效选择，使用默认微信扫码登录...
    cloudbase login
)

if %errorlevel% neq 0 (
    echo ❌ 登录失败
    pause
    exit /b 1
)

echo.
echo ✅ 登录成功！
echo.

echo 👤 当前登录状态：
cloudbase auth list

echo.
echo 📋 环境列表：
cloudbase env list

echo.
echo 🎉 登录完成！您现在可以使用以下命令：
echo - cloudbase env list          # 查看环境列表
echo - cloudbase functions list    # 查看云函数
echo - cloudbase storage list      # 查看存储文件
echo - cloudbase db collection list # 查看数据库集合
echo.

pause
