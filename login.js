#!/usr/bin/env node

const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚀 腾讯云开发环境登录助手');
console.log('================================');

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function checkCloudbaseCLI() {
  try {
    execSync('cloudbase --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

async function installCloudbaseCLI() {
  console.log('📦 正在安装腾讯云开发CLI...');
  try {
    execSync('npm install -g @cloudbase/cli', { stdio: 'inherit' });
    console.log('✅ CLI安装成功！');
    return true;
  } catch (error) {
    console.error('❌ CLI安装失败：', error.message);
    return false;
  }
}

async function loginToCloudbase() {
  console.log('\n🔐 开始登录腾讯云开发...');
  
  const loginMethod = await askQuestion('请选择登录方式：\n1. 微信扫码登录\n2. API密钥登录\n请输入选择 (1/2): ');
  
  try {
    if (loginMethod === '1') {
      console.log('请使用微信扫描二维码登录...');
      execSync('cloudbase login', { stdio: 'inherit' });
    } else if (loginMethod === '2') {
      console.log('使用API密钥登录...');
      execSync('cloudbase login --key', { stdio: 'inherit' });
    } else {
      console.log('❌ 无效选择，默认使用微信扫码登录');
      execSync('cloudbase login', { stdio: 'inherit' });
    }
    
    console.log('✅ 登录成功！');
    return true;
  } catch (error) {
    console.error('❌ 登录失败：', error.message);
    return false;
  }
}

async function showEnvironments() {
  console.log('\n📋 获取环境列表...');
  try {
    execSync('cloudbase env list', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ 获取环境列表失败：', error.message);
  }
}

async function showAuthStatus() {
  console.log('\n👤 当前登录状态：');
  try {
    execSync('cloudbase auth list', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ 获取登录状态失败：', error.message);
  }
}

async function main() {
  try {
    // 检查CLI是否已安装
    const cliInstalled = await checkCloudbaseCLI();
    
    if (!cliInstalled) {
      const shouldInstall = await askQuestion('未检测到腾讯云开发CLI，是否现在安装？(y/n): ');
      if (shouldInstall.toLowerCase() === 'y' || shouldInstall.toLowerCase() === 'yes') {
        const installed = await installCloudbaseCLI();
        if (!installed) {
          console.log('❌ 安装失败，请手动安装：npm install -g @cloudbase/cli');
          process.exit(1);
        }
      } else {
        console.log('❌ 需要先安装CLI才能继续');
        process.exit(1);
      }
    } else {
      console.log('✅ 腾讯云开发CLI已安装');
    }
    
    // 登录
    const loginSuccess = await loginToCloudbase();
    
    if (loginSuccess) {
      // 显示登录状态
      await showAuthStatus();
      
      // 显示环境列表
      await showEnvironments();
      
      console.log('\n🎉 登录完成！您现在可以使用以下命令：');
      console.log('- cloudbase env list          # 查看环境列表');
      console.log('- cloudbase functions list    # 查看云函数');
      console.log('- cloudbase storage list      # 查看存储文件');
      console.log('- cloudbase db collection list # 查看数据库集合');
    }
    
  } catch (error) {
    console.error('❌ 发生错误：', error.message);
  } finally {
    rl.close();
  }
}

// 运行主程序
main();
