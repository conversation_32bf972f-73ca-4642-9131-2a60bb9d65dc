# 腾讯云开发环境登录指南

## 方法一：使用腾讯云CLI登录

### 1. 安装腾讯云CLI
```bash
# 使用npm安装
npm install -g @cloudbase/cli

# 或者使用yarn安装
yarn global add @cloudbase/cli
```

### 2. 登录腾讯云开发
```bash
# 使用微信扫码登录
cloudbase login

# 或者使用腾讯云API密钥登录
cloudbase login --key
```

### 3. 验证登录状态
```bash
# 查看当前登录状态
cloudbase auth list

# 查看环境列表
cloudbase env list
```

## 方法二：使用腾讯云控制台

1. 打开浏览器访问：https://console.cloud.tencent.com/tcb
2. 使用腾讯云账号登录
3. 选择或创建云开发环境

## 方法三：使用微信开发者工具

1. 打开微信开发者工具
2. 选择"云开发"选项
3. 使用微信扫码登录

## 常用命令

### 环境管理
```bash
# 列出所有环境
cloudbase env list

# 创建新环境
cloudbase env create <envId>

# 删除环境
cloudbase env delete <envId>
```

### 函数管理
```bash
# 部署云函数
cloudbase functions deploy <functionName>

# 列出云函数
cloudbase functions list

# 调用云函数
cloudbase functions invoke <functionName>
```

### 数据库管理
```bash
# 导入数据
cloudbase db import <collectionName> <filePath>

# 导出数据
cloudbase db export <collectionName> <filePath>
```

### 存储管理
```bash
# 上传文件
cloudbase storage upload <localPath> <cloudPath>

# 下载文件
cloudbase storage download <cloudPath> <localPath>

# 列出文件
cloudbase storage list
```

## 配置文件示例

创建 `cloudbaserc.json` 配置文件：

```json
{
  "envId": "your-env-id",
  "functionRoot": "./functions",
  "functions": [
    {
      "name": "app",
      "timeout": 5,
      "envVariables": {},
      "runtime": "Nodejs10.15",
      "memorySize": 128
    }
  ]
}
```

## 环境变量配置

在项目根目录创建 `.env` 文件：

```env
# 腾讯云开发环境ID
TENCENT_CLOUD_ENV_ID=cloud1-8gjmztr75e2dfd27

# 腾讯云API密钥（可选）
TENCENT_CLOUD_SECRET_ID=your-secret-id
TENCENT_CLOUD_SECRET_KEY=your-secret-key
```

## 常见问题

### 1. 登录失败
- 检查网络连接
- 确认腾讯云账号状态
- 尝试清除缓存后重新登录

### 2. 环境ID获取
- 在腾讯云控制台的云开发页面可以找到环境ID
- 环境ID格式通常为：`env-xxxxxxxx`

### 3. 权限问题
- 确保账号有云开发的访问权限
- 检查子账号的权限配置

## 快速开始脚本

创建一个快速登录脚本 `login.js`：

```javascript
const cloudbase = require('@cloudbase/node-sdk');

// 初始化
const app = cloudbase.init({
  env: 'your-env-id'
});

// 测试连接
async function testConnection() {
  try {
    const db = app.database();
    const result = await db.collection('test').get();
    console.log('连接成功！', result);
  } catch (error) {
    console.error('连接失败：', error);
  }
}

testConnection();
```
