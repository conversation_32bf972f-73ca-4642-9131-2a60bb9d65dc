<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邱大山，你真棒！- Modern Three.js</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: 'Courier New', monospace;
            color: #fff;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            font-size: 13px;
            line-height: 1.4;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
        }
        
        #info a {
            color: #ff6600;
            text-decoration: none;
        }
        
        #info a:hover {
            text-decoration: underline;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
            text-align: center;
            background: rgba(0,0,0,0.7);
            padding: 20px;
            border-radius: 10px;
        }
        
        .loading-bar {
            width: 200px;
            height: 4px;
            background: #333;
            margin: 15px auto;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #ff6600, #ffaa00);
            width: 0%;
            animation: loading 2s ease-in-out infinite;
        }
        
        @keyframes loading {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        
        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            z-index: 100;
            font-size: 11px;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <div>Loading Modern Three.js...</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
        
        <div id="info">
            <strong>Modern Three.js ES Modules</strong><br/>
            3D Text: "邱大山，你真棒！"<br/>
            <a href="https://threejs.org" target="_blank" rel="noopener">three.js</a> r158
        </div>
        
        <div id="controls">
            Mouse: rotate view<br/>
            Scroll: zoom<br/>
            Space: auto-rotate<br/>
            R: reset camera<br/>
            C: Chinese text<br/>
            E: English text
        </div>
    </div>

    <!-- 使用ES模块方式加载Three.js -->
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>
    
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { FontLoader } from 'three/addons/loaders/FontLoader.js';
        import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';

        let camera, scene, renderer, controls;
        let group, textMeshes = [];
        let font = null;

        const text = "邱大山，你真棒！";
        const textParams = {
            size: 60,
            height: 15,
            curveSegments: 4,
            bevelEnabled: true,
            bevelThickness: 1,
            bevelSize: 0.5
        };

        let targetRotation = 0;
        let mouseX = 0, mouseY = 0;
        let windowHalfX = window.innerWidth / 2;
        let windowHalfY = window.innerHeight / 2;

        init();
        animate();

        function init() {
            console.log('Initializing Modern Three.js...');
            
            // 场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x000000);
            scene.fog = new THREE.Fog(0x000000, 250, 1400);

            // 相机
            camera = new THREE.PerspectiveCamera(30, window.innerWidth / window.innerHeight, 1, 1500);
            camera.position.set(0, 400, 700);

            // 渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('container').appendChild(renderer.domElement);

            // 控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.target.set(0, 0, 0);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;

            // 光照
            setupLights();

            // 创建文字组
            group = new THREE.Group();
            scene.add(group);

            // 直接创建中文文字
            createChineseTextMeshes();

            // 事件监听
            setupEventListeners();

            // 隐藏加载界面
            setTimeout(() => {
                const loading = document.getElementById('loading');
                if (loading) loading.style.display = 'none';
            }, 1000);
        }

        function setupLights() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);

            // 方向光
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1).normalize();
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 点光源
            const pointLight = new THREE.PointLight(0xffffff, 1, 1000);
            pointLight.position.set(0, 100, 90);
            scene.add(pointLight);
        }

        function createChineseTextMeshes() {
            console.log('Creating Chinese text meshes...');
            const characters = ['邱', '大', '山', '你', '真', '棒', '！'];
            const spacing = 120;
            const startX = -(characters.length - 1) * spacing / 2;

            characters.forEach((char, index) => {
                const mesh = createChineseCharacterMesh(char);
                mesh.position.x = startX + index * spacing;
                mesh.position.y = 0;
                mesh.position.z = 0;
                
                group.add(mesh);
                textMeshes.push(mesh);
            });
        }

        function createChineseCharacterMesh(character) {
            // 创建基础几何体
            const geometry = new THREE.ExtrudeGeometry(
                createCharacterShape(),
                {
                    depth: textParams.height,
                    bevelEnabled: textParams.bevelEnabled,
                    bevelThickness: textParams.bevelThickness,
                    bevelSize: textParams.bevelSize,
                    bevelSegments: 3,
                    curveSegments: textParams.curveSegments
                }
            );

            // 创建文字纹理
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 256;
            canvas.height = 256;

            // 绘制文字
            context.fillStyle = '#ffffff';
            context.font = 'bold 180px "Microsoft YaHei", "SimHei", "黑体", Arial, sans-serif';
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillText(character, canvas.width/2, canvas.height/2);

            const texture = new THREE.CanvasTexture(canvas);

            // 创建材质
            const materials = [
                new THREE.MeshPhongMaterial({ map: texture, color: 0xffffff }), // 正面
                new THREE.MeshPhongMaterial({ color: 0x666666 }) // 侧面
            ];

            const mesh = new THREE.Mesh(geometry, materials);
            mesh.castShadow = true;
            mesh.receiveShadow = true;

            return mesh;
        }

        function createCharacterShape() {
            const shape = new THREE.Shape();
            const size = textParams.size;
            
            // 创建圆角矩形形状
            const x = -size/2, y = -size/2;
            const width = size, height = size;
            const radius = size * 0.1;

            shape.moveTo(x, y + radius);
            shape.lineTo(x, y + height - radius);
            shape.quadraticCurveTo(x, y + height, x + radius, y + height);
            shape.lineTo(x + width - radius, y + height);
            shape.quadraticCurveTo(x + width, y + height, x + width, y + height - radius);
            shape.lineTo(x + width, y + radius);
            shape.quadraticCurveTo(x + width, y, x + width - radius, y);
            shape.lineTo(x + radius, y);
            shape.quadraticCurveTo(x, y, x, y + radius);

            return shape;
        }

        function setupEventListeners() {
            // 鼠标事件
            document.addEventListener('mousemove', onDocumentMouseMove, false);
            document.addEventListener('mousedown', onDocumentMouseDown, false);
            
            // 键盘事件
            document.addEventListener('keydown', onDocumentKeyDown, false);
            
            // 窗口大小调整
            window.addEventListener('resize', onWindowResize, false);
        }

        function onDocumentMouseMove(event) {
            mouseX = (event.clientX - windowHalfX) * 0.001;
            mouseY = (event.clientY - windowHalfY) * 0.001;
        }

        function onDocumentMouseDown(event) {
            targetRotation += Math.PI * 0.1;
        }

        function onDocumentKeyDown(event) {
            switch(event.code) {
                case 'Space':
                    event.preventDefault();
                    targetRotation += Math.PI * 2;
                    break;
                case 'KeyR':
                    camera.position.set(0, 400, 700);
                    targetRotation = 0;
                    group.rotation.y = 0;
                    break;
            }
        }

        function onWindowResize() {
            windowHalfX = window.innerWidth / 2;
            windowHalfY = window.innerHeight / 2;

            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();

            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);

            const time = Date.now() * 0.001;

            // 平滑旋转
            group.rotation.y += (targetRotation - group.rotation.y) * 0.05;

            // 文字动画
            textMeshes.forEach((mesh, index) => {
                const offset = index * 0.3;
                
                // 浮动效果
                mesh.position.y = Math.sin(time * 2 + offset) * 20;
                
                // 独立旋转
                mesh.rotation.z = Math.sin(time * 1.5 + offset) * 0.1;
                
                // 颜色变化
                if (mesh.material && mesh.material[0]) {
                    const hue = (time * 0.2 + index * 0.1) % 1;
                    mesh.material[0].color.setHSL(hue, 0.8, 0.7);
                }
            });

            // 相机跟随鼠标
            camera.position.x += (mouseX * 200 - camera.position.x) * 0.05;
            camera.position.y += (-mouseY * 200 - camera.position.y) * 0.05;

            // 更新控制器
            controls.update();

            // 渲染
            renderer.render(scene, camera);
        }
    </script>
</body>
</html>
