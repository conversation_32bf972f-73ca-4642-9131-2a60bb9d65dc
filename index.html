<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邱大山，你真棒！- 3D文字几何体</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: 'Courier New', monospace;
            color: #fff;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            font-size: 13px;
            line-height: 1.4;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
        }

        #info a {
            color: #ff6600;
            text-decoration: none;
        }

        #info a:hover {
            text-decoration: underline;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 200;
            text-align: center;
            background: rgba(0,0,0,0.7);
            padding: 20px;
            border-radius: 10px;
        }

        .loading-bar {
            width: 200px;
            height: 4px;
            background: #333;
            margin: 15px auto;
            border-radius: 2px;
            overflow: hidden;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #ff6600, #ffaa00);
            width: 0%;
            animation: loading 2s ease-in-out infinite;
        }

        @keyframes loading {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            z-index: 100;
            font-size: 11px;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <div>Loading fonts and geometries...</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>

        <div id="info">
            <strong>webgl_geometry_text_shapes</strong><br/>
            3D Text Geometry with Chinese Characters<br/>
            <a href="https://threejs.org" target="_blank" rel="noopener">three.js</a> -
            Text: "邱大山，你真棒！"
        </div>

        <div id="controls">
            Mouse: rotate text<br/>
            Scroll: zoom camera<br/>
            Space: auto-rotation<br/>
            R: reset camera<br/>
            C: Chinese text<br/>
            E: English text<br/>
            Click: rotate text
        </div>
    </div>

    <!-- 使用ES模块方式加载Three.js -->
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
            }
        }
    </script>

    <script type="module" src="text-geometry-modern.js"></script>
</body>
</html>
